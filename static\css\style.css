/* DBC文件管理系统样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

.navbar-brand i {
    margin-right: 8px;
}

/* 页面内容样式 */
.page-content {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn i {
    margin-right: 4px;
}

.btn-sm i {
    margin-right: 2px;
}

/* 文件上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

/* 文件图标样式 */
.file-icon {
    font-size: 2rem;
    color: #6c757d;
}

.file-icon.dbc {
    color: #28a745;
}

/* 版本标签样式 */
.version-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 状态指示器 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 文件大小显示 */
.file-size {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #6c757d;
}

/* 时间显示 */
.time-display {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 操作按钮组 */
.action-buttons {
    white-space: nowrap;
}

.action-buttons .btn {
    margin-right: 0.25rem;
}

.action-buttons .btn:last-child {
    margin-right: 0;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    padding-left: 2.5rem;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
}

/* 版本历史样式 */
.version-history {
    max-height: 400px;
    overflow-y: auto;
}

.version-item {
    border-left: 3px solid #dee2e6;
    padding-left: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.version-item.current {
    border-left-color: #007bff;
}

.version-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0.5rem;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background-color: #dee2e6;
}

.version-item.current::before {
    background-color: #007bff;
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* 管理后台样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.sidebar .nav-link:hover {
    color: #007bff;
}

/* 边框样式 */
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}

.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}

.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-buttons .btn {
        margin-right: 0;
        width: 100%;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    /* 移动端表格优化 */
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }

    .table th {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
    }

    /* 移动端卡片间距 */
    .card {
        margin-bottom: 1rem;
    }

    /* 移动端按钮组 */
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* 移动端模态框 */
    .modal-dialog {
        margin: 0.5rem;
    }

    /* 移动端导航栏 */
    .navbar-nav {
        text-align: center;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }

    /* 移动端搜索框 */
    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .d-flex.gap-2 input {
        width: 100% !important;
    }

    /* 移动端统计卡片 */
    .stats-card {
        margin-bottom: 1rem;
    }

    /* 移动端版本历史 */
    .version-history {
        max-height: 300px;
    }
}

@media (max-width: 576px) {
    /* 超小屏幕优化 */
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .card-body {
        padding: 1rem 0.75rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    /* 超小屏幕表格 */
    .table-responsive {
        border: none;
    }

    .table {
        font-size: 0.75rem;
    }

    .table td, .table th {
        padding: 0.25rem;
    }

    /* 超小屏幕操作按钮 */
    .action-buttons .btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    /* 超小屏幕模态框 */
    .modal-dialog {
        margin: 0.25rem;
        max-width: none;
        width: calc(100% - 0.5rem);
    }

    /* 超小屏幕表单 */
    .form-control {
        font-size: 0.875rem;
    }

    .form-label {
        font-size: 0.875rem;
        font-weight: 600;
    }
}

/* 平板设备优化 */
@media (min-width: 768px) and (max-width: 1024px) {
    .container {
        max-width: 100%;
    }

    .table-responsive {
        font-size: 0.9rem;
    }

    .btn-group .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .sidebar {
        width: 200px;
    }

    main {
        margin-left: 200px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }

    .btn-sm {
        min-height: 36px;
        min-width: 36px;
    }

    .table-hover tbody tr:hover {
        background-color: transparent;
    }

    .nav-link:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .sidebar,
    .action-buttons,
    .btn,
    .modal {
        display: none !important;
    }

    .container,
    .container-fluid {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
