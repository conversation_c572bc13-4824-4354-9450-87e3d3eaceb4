from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class DBCFileVersionBase(BaseModel):
    version_number: int
    comment: Optional[str] = None
    created_by: Optional[str] = None


class DBCFileVersionCreate(DBCFileVersionBase):
    pass


class DBCFileVersion(DBCFileVersionBase):
    id: int
    dbc_file_id: int
    file_path: str
    file_size: int
    file_hash: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class DBCFileBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None


class DBCFileCreate(DBCFileBase):
    pass


class DBCFileUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None


class DBCFile(DBCFileBase):
    id: int
    original_filename: str
    created_at: datetime
    updated_at: Optional[datetime]
    is_deleted: bool
    versions: List[DBCFileVersion] = []
    
    class Config:
        from_attributes = True


class DBCFileList(BaseModel):
    id: int
    name: str
    original_filename: str
    description: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    latest_version_number: Optional[int] = None
    file_size: Optional[int] = None
    
    class Config:
        from_attributes = True


class UploadResponse(BaseModel):
    message: str
    file_id: int
    version_id: int
    version_number: int


class ErrorResponse(BaseModel):
    detail: str
