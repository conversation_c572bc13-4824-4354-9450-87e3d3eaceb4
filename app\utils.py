import hashlib
import os
import shutil
from typing import Optional
import cantools
from fastapi import HTTPException


def calculate_file_hash(file_path: str) -> str:
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def validate_dbc_file(file_path: str) -> bool:
    """验证DBC文件格式是否正确"""
    try:
        # 使用cantools库验证DBC文件
        cantools.database.load_file(file_path)
        return True
    except Exception as e:
        print(f"DBC validation error: {e}")
        return False


def get_file_size(file_path: str) -> int:
    """获取文件大小"""
    return os.path.getsize(file_path)


def ensure_directory_exists(directory: str) -> None:
    """确保目录存在"""
    os.makedirs(directory, exist_ok=True)


def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    # 移除或替换不安全的字符
    import re
    filename = re.sub(r'[^\w\s-.]', '', filename)
    filename = re.sub(r'[-\s]+', '-', filename)
    return filename.strip('-.')


def copy_file_to_storage(source_path: str, destination_path: str) -> None:
    """安全地复制文件到存储位置"""
    ensure_directory_exists(os.path.dirname(destination_path))
    shutil.copy2(source_path, destination_path)


def delete_file_safe(file_path: str) -> bool:
    """安全地删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        print(f"Error deleting file {file_path}: {e}")
        return False
