import os
import tempfile
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import DBCFile, DBCFileVersion
from app.schemas import (
    DBCFile as DBCFileSchema,
    DBCFileCreate,
    DBCFileUpdate,
    DBCFileList,
    UploadResponse,
    ErrorResponse
)
from app.utils import (
    calculate_file_hash,
    validate_dbc_file,
    get_file_size,
    safe_filename,
    copy_file_to_storage,
    delete_file_safe
)
from app.config import settings

router = APIRouter()


@router.post("/dbc-files/upload", response_model=UploadResponse)
async def upload_dbc_file(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    comment: Optional[str] = Form(None),
    created_by: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """上传DBC文件"""
    
    # 验证文件类型
    if not file.filename.lower().endswith('.dbc'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只支持.dbc文件格式"
        )
    
    # 验证文件大小
    if file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"文件大小超过限制 ({settings.max_file_size} bytes)"
        )
    
    # 创建临时文件进行验证
    with tempfile.NamedTemporaryFile(delete=False, suffix='.dbc') as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_file_path = temp_file.name
    
    try:
        # 验证DBC文件格式
        if not validate_dbc_file(temp_file_path):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的DBC文件格式"
            )
        
        # 计算文件哈希
        file_hash = calculate_file_hash(temp_file_path)
        file_size = get_file_size(temp_file_path)
        
        # 检查是否已存在同名文件
        existing_file = db.query(DBCFile).filter(
            DBCFile.name == name,
            DBCFile.is_deleted == False
        ).first()
        
        if existing_file:
            # 检查是否为相同文件（通过哈希值）
            latest_version = existing_file.latest_version
            if latest_version and latest_version.file_hash == file_hash:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="相同内容的文件已存在"
                )
            
            # 创建新版本
            new_version_number = max([v.version_number for v in existing_file.versions]) + 1
            dbc_file = existing_file
        else:
            # 创建新文件记录
            dbc_file = DBCFile(
                name=name,
                original_filename=file.filename,
                description=description
            )
            db.add(dbc_file)
            db.flush()  # 获取ID
            new_version_number = 1
        
        # 生成存储路径
        safe_name = safe_filename(f"{dbc_file.id}_{new_version_number}_{file.filename}")
        storage_path = os.path.join(settings.upload_dir, safe_name)
        
        # 复制文件到存储位置
        copy_file_to_storage(temp_file_path, storage_path)
        
        # 创建版本记录
        file_version = DBCFileVersion(
            dbc_file_id=dbc_file.id,
            version_number=new_version_number,
            file_path=storage_path,
            file_size=file_size,
            file_hash=file_hash,
            comment=comment,
            created_by=created_by
        )
        
        db.add(file_version)
        db.commit()
        
        return UploadResponse(
            message="文件上传成功",
            file_id=dbc_file.id,
            version_id=file_version.id,
            version_number=new_version_number
        )

    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


@router.get("/dbc-files", response_model=List[DBCFileList])
async def list_dbc_files(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取DBC文件列表"""
    query = db.query(DBCFile).filter(DBCFile.is_deleted == False)

    if search:
        query = query.filter(DBCFile.name.contains(search))

    files = query.offset(skip).limit(limit).all()

    result = []
    for file in files:
        latest_version = file.latest_version
        result.append(DBCFileList(
            id=file.id,
            name=file.name,
            original_filename=file.original_filename,
            description=file.description,
            created_at=file.created_at,
            updated_at=file.updated_at,
            latest_version_number=latest_version.version_number if latest_version else None,
            file_size=latest_version.file_size if latest_version else None
        ))

    return result


@router.get("/dbc-files/{file_id}", response_model=DBCFileSchema)
async def get_dbc_file(file_id: int, db: Session = Depends(get_db)):
    """获取DBC文件详情"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    return dbc_file


@router.put("/dbc-files/{file_id}", response_model=DBCFileSchema)
async def update_dbc_file(
    file_id: int,
    file_update: DBCFileUpdate,
    db: Session = Depends(get_db)
):
    """更新DBC文件信息（重命名、修改描述）"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 检查新名称是否已存在
    if file_update.name and file_update.name != dbc_file.name:
        existing = db.query(DBCFile).filter(
            DBCFile.name == file_update.name,
            DBCFile.is_deleted == False,
            DBCFile.id != file_id
        ).first()

        if existing:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="文件名已存在"
            )

    # 更新字段
    update_data = file_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(dbc_file, field, value)

    dbc_file.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(dbc_file)

    return dbc_file


@router.delete("/dbc-files/{file_id}")
async def delete_dbc_file(file_id: int, db: Session = Depends(get_db)):
    """删除DBC文件（软删除）"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 软删除
    dbc_file.is_deleted = True
    dbc_file.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "文件删除成功"}


@router.delete("/dbc-files/{file_id}/permanent")
async def permanently_delete_dbc_file(file_id: int, db: Session = Depends(get_db)):
    """永久删除DBC文件及其所有版本"""
    dbc_file = db.query(DBCFile).filter(DBCFile.id == file_id).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 删除所有版本的物理文件
    for version in dbc_file.versions:
        delete_file_safe(version.file_path)

    # 删除数据库记录
    db.delete(dbc_file)
    db.commit()

    return {"message": "文件永久删除成功"}


@router.get("/dbc-files/{file_id}/download")
async def download_dbc_file(
    file_id: int,
    version: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """下载DBC文件"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 获取指定版本或最新版本
    if version:
        file_version = db.query(DBCFileVersion).filter(
            DBCFileVersion.dbc_file_id == file_id,
            DBCFileVersion.version_number == version
        ).first()
    else:
        file_version = dbc_file.latest_version

    if not file_version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定版本不存在"
        )

    if not os.path.exists(file_version.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在于服务器"
        )

    return FileResponse(
        path=file_version.file_path,
        filename=dbc_file.original_filename,
        media_type='application/octet-stream'
    )


@router.get("/dbc-files/{file_id}/versions")
async def get_file_versions(file_id: int, db: Session = Depends(get_db)):
    """获取文件的所有版本"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    versions = db.query(DBCFileVersion).filter(
        DBCFileVersion.dbc_file_id == file_id
    ).order_by(DBCFileVersion.version_number.desc()).all()

    return versions


@router.post("/dbc-files/{file_id}/versions/{version_number}/rollback")
async def rollback_to_version(
    file_id: int,
    version_number: int,
    comment: Optional[str] = None,
    created_by: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """回滚到指定版本（创建新版本）"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 查找要回滚的版本
    target_version = db.query(DBCFileVersion).filter(
        DBCFileVersion.dbc_file_id == file_id,
        DBCFileVersion.version_number == version_number
    ).first()

    if not target_version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定版本不存在"
        )

    if not os.path.exists(target_version.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="版本文件不存在于服务器"
        )

    # 创建新版本号
    max_version = max([v.version_number for v in dbc_file.versions])
    new_version_number = max_version + 1

    # 生成新的存储路径
    original_filename = dbc_file.original_filename
    safe_name = safe_filename(f"{file_id}_{new_version_number}_{original_filename}")
    new_storage_path = os.path.join(settings.upload_dir, safe_name)

    # 复制目标版本文件
    copy_file_to_storage(target_version.file_path, new_storage_path)

    # 创建新版本记录
    new_version = DBCFileVersion(
        dbc_file_id=file_id,
        version_number=new_version_number,
        file_path=new_storage_path,
        file_size=target_version.file_size,
        file_hash=target_version.file_hash,
        comment=comment or f"回滚到版本 {version_number}",
        created_by=created_by
    )

    db.add(new_version)
    dbc_file.updated_at = datetime.utcnow()
    db.commit()

    return {
        "message": f"成功回滚到版本 {version_number}",
        "new_version_number": new_version_number,
        "version_id": new_version.id
    }


@router.delete("/dbc-files/{file_id}/versions/{version_number}")
async def delete_file_version(
    file_id: int,
    version_number: int,
    db: Session = Depends(get_db)
):
    """删除指定版本（不能删除最后一个版本）"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == False
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 检查版本数量
    if len(dbc_file.versions) <= 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除最后一个版本"
        )

    # 查找要删除的版本
    version_to_delete = db.query(DBCFileVersion).filter(
        DBCFileVersion.dbc_file_id == file_id,
        DBCFileVersion.version_number == version_number
    ).first()

    if not version_to_delete:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定版本不存在"
        )

    # 删除物理文件
    delete_file_safe(version_to_delete.file_path)

    # 删除数据库记录
    db.delete(version_to_delete)
    db.commit()

    return {"message": f"版本 {version_number} 删除成功"}


@router.get("/dbc-files/{file_id}/versions/{version_number}")
async def get_file_version_detail(
    file_id: int,
    version_number: int,
    db: Session = Depends(get_db)
):
    """获取指定版本的详细信息"""
    version = db.query(DBCFileVersion).filter(
        DBCFileVersion.dbc_file_id == file_id,
        DBCFileVersion.version_number == version_number
    ).first()

    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定版本不存在"
        )

    return version


@router.get("/dbc-files/stats")
async def get_files_statistics(db: Session = Depends(get_db)):
    """获取文件统计信息"""
    total_files = db.query(DBCFile).filter(DBCFile.is_deleted == False).count()
    total_versions = db.query(DBCFileVersion).join(DBCFile).filter(
        DBCFile.is_deleted == False
    ).count()

    # 计算总存储大小
    total_size = db.query(
        db.func.sum(DBCFileVersion.file_size)
    ).join(DBCFile).filter(DBCFile.is_deleted == False).scalar() or 0

    return {
        "total_files": total_files,
        "total_versions": total_versions,
        "total_storage_size": total_size,
        "total_storage_size_mb": round(total_size / (1024 * 1024), 2)
    }


@router.post("/dbc-files/{file_id}/restore")
async def restore_deleted_file(file_id: int, db: Session = Depends(get_db)):
    """恢复已删除的文件"""
    dbc_file = db.query(DBCFile).filter(
        DBCFile.id == file_id,
        DBCFile.is_deleted == True
    ).first()

    if not dbc_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在或未被删除"
        )

    # 检查是否有同名文件存在
    existing = db.query(DBCFile).filter(
        DBCFile.name == dbc_file.name,
        DBCFile.is_deleted == False
    ).first()

    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="存在同名文件，无法恢复"
        )

    dbc_file.is_deleted = False
    dbc_file.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "文件恢复成功"}


@router.get("/dbc-files/deleted")
async def list_deleted_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取已删除的文件列表"""
    files = db.query(DBCFile).filter(
        DBCFile.is_deleted == True
    ).offset(skip).limit(limit).all()

    result = []
    for file in files:
        latest_version = file.latest_version
        result.append(DBCFileList(
            id=file.id,
            name=file.name,
            original_filename=file.original_filename,
            description=file.description,
            created_at=file.created_at,
            updated_at=file.updated_at,
            latest_version_number=latest_version.version_number if latest_version else None,
            file_size=latest_version.file_size if latest_version else None
        ))

    return result
