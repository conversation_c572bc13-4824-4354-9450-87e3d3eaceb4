from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from app.config import settings
from app.database import engine, Base
from app.routers import dbc_files
import uvicorn
import os

# Create database tables
Base.metadata.create_all(bind=engine)

# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="DBC文件管理系统 - 支持DBC文件上传、版本管理、删除和重命名等操作",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
static_dir = "static"
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Include routers
app.include_router(dbc_files.router, prefix="/api/v1", tags=["DBC Files"])


@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 返回主页面"""
    # 如果存在静态文件，返回主页面
    index_path = os.path.join("static", "index.html")
    if os.path.exists(index_path):
        with open(index_path, "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())

    # 否则返回简单的欢迎页面
    return HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{settings.app_name}</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>欢迎使用 {settings.app_name}</h1>
        <p>版本: {settings.app_version}</p>
        <p><a href="/docs">API 文档</a></p>
        <p><a href="/admin">管理界面</a></p>
    </body>
    </html>
    """)


@app.get("/admin", response_class=HTMLResponse)
async def admin_page():
    """管理界面"""
    admin_path = os.path.join("static", "admin.html")
    if os.path.exists(admin_path):
        with open(admin_path, "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())

    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>DBC文件管理系统</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>DBC文件管理系统</h1>
        <p>管理界面正在加载中...</p>
        <p><a href="/docs">查看API文档</a></p>
    </body>
    </html>
    """)


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": settings.app_name}


# Global exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
