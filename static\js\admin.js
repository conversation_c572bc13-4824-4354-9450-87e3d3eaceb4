// DBC文件管理系统管理后台JavaScript

// API基础URL
const API_BASE = '/api/v1';

// 全局变量
let currentAdminFiles = [];
let currentPage = 'adminFileList';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    showAdminFileList();
    
    // 绑定事件
    bindAdminEvents();
});

// 绑定事件
function bindAdminEvents() {
    // 搜索功能
    const searchInput = document.getElementById('adminSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterAdminFileList(searchTerm);
        });
    }
}

// 显示文件管理页面
function showAdminFileList() {
    hideAllAdminPages();
    document.getElementById('adminFileListPage').style.display = 'block';
    document.getElementById('pageTitle').textContent = '文件管理';
    currentPage = 'adminFileList';
    
    // 更新导航状态
    updateAdminNavigation('adminFileList');
    
    // 加载文件列表
    loadAdminFileList();
}

// 显示回收站页面
function showDeletedFiles() {
    hideAllAdminPages();
    document.getElementById('deletedFilesPage').style.display = 'block';
    document.getElementById('pageTitle').textContent = '回收站';
    currentPage = 'deletedFiles';
    
    // 更新导航状态
    updateAdminNavigation('deletedFiles');
    
    // 加载已删除文件列表
    loadDeletedFiles();
}

// 显示统计页面
function showAdminStats() {
    hideAllAdminPages();
    document.getElementById('adminStatsPage').style.display = 'block';
    document.getElementById('pageTitle').textContent = '系统统计';
    currentPage = 'adminStats';
    
    // 更新导航状态
    updateAdminNavigation('adminStats');
    
    // 加载统计数据
    loadAdminStats();
}

// 显示系统信息页面
function showSystemInfo() {
    hideAllAdminPages();
    document.getElementById('systemInfoPage').style.display = 'block';
    document.getElementById('pageTitle').textContent = '系统信息';
    currentPage = 'systemInfo';
    
    // 更新导航状态
    updateAdminNavigation('systemInfo');
}

// 隐藏所有管理页面
function hideAllAdminPages() {
    const pages = document.querySelectorAll('.admin-page');
    pages.forEach(page => page.style.display = 'none');
}

// 更新导航状态
function updateAdminNavigation(activePage) {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    
    // 根据页面设置活动状态
    const pageMap = {
        'adminFileList': 0,
        'deletedFiles': 1,
        'adminStats': 2,
        'systemInfo': 3
    };
    
    if (pageMap[activePage] !== undefined) {
        navLinks[pageMap[activePage]].classList.add('active');
    }
}

// 加载管理文件列表
async function loadAdminFileList() {
    try {
        const response = await fetch(`${API_BASE}/dbc-files`);
        if (!response.ok) {
            throw new Error('加载文件列表失败');
        }
        
        const files = await response.json();
        currentAdminFiles = files;
        
        displayAdminFileList(files);
        
    } catch (error) {
        console.error('Error loading admin file list:', error);
        showAdminToast('加载文件列表失败: ' + error.message, 'error');
    }
}

// 显示管理文件列表
function displayAdminFileList(files) {
    const tbody = document.getElementById('adminFileListBody');
    
    if (files.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="bi bi-folder-x display-4"></i>
                    <p class="mt-2">暂无文件</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = files.map(file => `
        <tr>
            <td>${file.id}</td>
            <td>
                <i class="bi bi-file-earmark-code text-success me-2"></i>
                <strong>${escapeHtml(file.name)}</strong>
            </td>
            <td>${escapeHtml(file.original_filename)}</td>
            <td>
                <span class="badge bg-info">${file.latest_version_number || 0}</span>
            </td>
            <td class="file-size">${formatFileSize(file.file_size)}</td>
            <td class="time-display">${formatDateTime(file.created_at)}</td>
            <td class="time-display">${formatDateTime(file.updated_at)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewAdminFileDetail(${file.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="manageVersions(${file.id})" title="版本管理">
                        <i class="bi bi-layers"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="downloadAdminFile(${file.id})" title="下载">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteAdminFile(${file.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="permanentDeleteFile(${file.id})" title="永久删除">
                        <i class="bi bi-x-circle"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 过滤管理文件列表
function filterAdminFileList(searchTerm) {
    if (!searchTerm) {
        displayAdminFileList(currentAdminFiles);
        return;
    }
    
    const filteredFiles = currentAdminFiles.filter(file => 
        file.name.toLowerCase().includes(searchTerm) ||
        file.original_filename.toLowerCase().includes(searchTerm) ||
        file.id.toString().includes(searchTerm)
    );
    
    displayAdminFileList(filteredFiles);
}

// 加载已删除文件列表
async function loadDeletedFiles() {
    try {
        const response = await fetch(`${API_BASE}/dbc-files/deleted`);
        if (!response.ok) {
            throw new Error('加载已删除文件列表失败');
        }
        
        const files = await response.json();
        displayDeletedFiles(files);
        
    } catch (error) {
        console.error('Error loading deleted files:', error);
        showAdminToast('加载已删除文件列表失败: ' + error.message, 'error');
    }
}

// 显示已删除文件列表
function displayDeletedFiles(files) {
    const tbody = document.getElementById('deletedFilesBody');
    
    if (files.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="bi bi-trash display-4"></i>
                    <p class="mt-2">回收站为空</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = files.map(file => `
        <tr>
            <td>${file.id}</td>
            <td>
                <i class="bi bi-file-earmark-code text-muted me-2"></i>
                ${escapeHtml(file.name)}
            </td>
            <td>${escapeHtml(file.original_filename)}</td>
            <td class="time-display">${formatDateTime(file.updated_at)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-success" onclick="restoreFile(${file.id})" title="恢复文件">
                        <i class="bi bi-arrow-clockwise"></i> 恢复
                    </button>
                    <button class="btn btn-outline-danger" onclick="permanentDeleteFile(${file.id})" title="永久删除">
                        <i class="bi bi-x-circle"></i> 永久删除
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 加载管理统计数据
async function loadAdminStats() {
    try {
        const response = await fetch(`${API_BASE}/dbc-files/stats`);
        if (!response.ok) {
            throw new Error('获取统计信息失败');
        }
        
        const stats = await response.json();
        
        document.getElementById('adminTotalFiles').textContent = stats.total_files;
        document.getElementById('adminTotalVersions').textContent = stats.total_versions;
        document.getElementById('adminTotalSize').textContent = stats.total_storage_size_mb + ' MB';
        
    } catch (error) {
        console.error('Error loading admin stats:', error);
        showAdminToast('获取统计信息失败: ' + error.message, 'error');
    }
}

// 删除文件（软删除）
async function deleteAdminFile(fileId) {
    if (!confirm('确定要删除这个文件吗？删除后可以在回收站中恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '删除失败');
        }
        
        showAdminToast('文件删除成功', 'success');
        loadAdminFileList();
        
    } catch (error) {
        console.error('Delete error:', error);
        showAdminToast('删除失败: ' + error.message, 'error');
    }
}

// 永久删除文件
async function permanentDeleteFile(fileId) {
    if (!confirm('确定要永久删除这个文件吗？此操作不可恢复，将删除所有版本和物理文件。')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}/permanent`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '永久删除失败');
        }
        
        showAdminToast('文件永久删除成功', 'success');
        
        // 刷新当前页面
        if (currentPage === 'adminFileList') {
            loadAdminFileList();
        } else if (currentPage === 'deletedFiles') {
            loadDeletedFiles();
        }
        
    } catch (error) {
        console.error('Permanent delete error:', error);
        showAdminToast('永久删除失败: ' + error.message, 'error');
    }
}

// 恢复文件
async function restoreFile(fileId) {
    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}/restore`, {
            method: 'POST'
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '恢复失败');
        }
        
        showAdminToast('文件恢复成功', 'success');
        loadDeletedFiles();
        
    } catch (error) {
        console.error('Restore error:', error);
        showAdminToast('恢复失败: ' + error.message, 'error');
    }
}

// 下载文件
async function downloadAdminFile(fileId) {
    try {
        const url = `${API_BASE}/dbc-files/${fileId}/download`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error('下载失败');
        }
        
        // 获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        let filename = 'download.dbc';
        if (contentDisposition) {
            const matches = contentDisposition.match(/filename="(.+)"/);
            if (matches) {
                filename = matches[1];
            }
        }
        
        // 下载文件
        const blob = await response.blob();
        const url2 = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url2;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url2);
        document.body.removeChild(a);
        
        showAdminToast('文件下载成功', 'success');
        
    } catch (error) {
        console.error('Download error:', error);
        showAdminToast('下载失败: ' + error.message, 'error');
    }
}

// 刷新当前页面
function refreshCurrentPage() {
    switch (currentPage) {
        case 'adminFileList':
            loadAdminFileList();
            break;
        case 'deletedFiles':
            loadDeletedFiles();
            break;
        case 'adminStats':
            loadAdminStats();
            break;
        default:
            break;
    }
}

// 工具函数
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatFileSize(bytes) {
    if (!bytes) return '-';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showAdminToast(message, type = 'info') {
    // 创建临时Toast元素
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast ${type === 'success' ? 'bg-success text-white' : type === 'error' ? 'bg-danger text-white' : ''}" role="alert">
            <div class="toast-header">
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const bsToast = new bootstrap.Toast(toastElement);
    bsToast.show();
    
    // 自动清理
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}
