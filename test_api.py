#!/usr/bin/env python3
"""
DBC文件管理系统API测试脚本
"""

import requests
import json
import os

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """测试健康检查"""
    response = requests.get("http://localhost:8000/health")
    print(f"健康检查: {response.status_code} - {response.json()}")

def test_upload_file():
    """测试文件上传"""
    # 创建一个简单的测试DBC文件
    test_dbc_content = """VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_:


BO_ 100 TestMessage: 8 Vector__XXX
 SG_ TestSignal : 0|8@1+ (1,0) [0|255] "" Vector__XXX

"""
    
    # 保存测试文件
    test_file_path = "test.dbc"
    with open(test_file_path, "w") as f:
        f.write(test_dbc_content)
    
    try:
        # 上传文件
        with open(test_file_path, "rb") as f:
            files = {"file": ("test.dbc", f, "application/octet-stream")}
            data = {
                "name": "测试DBC文件",
                "description": "这是一个测试用的DBC文件",
                "comment": "初始版本",
                "created_by": "测试用户"
            }
            response = requests.post(f"{BASE_URL}/dbc-files/upload", files=files, data=data)
            print(f"文件上传: {response.status_code} - {response.json()}")
            return response.json().get("file_id") if response.status_code == 200 else None
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_list_files():
    """测试文件列表"""
    response = requests.get(f"{BASE_URL}/dbc-files")
    print(f"文件列表: {response.status_code}")
    if response.status_code == 200:
        files = response.json()
        print(f"找到 {len(files)} 个文件")
        for file in files:
            print(f"  - {file['name']} (ID: {file['id']})")

def test_get_file_detail(file_id):
    """测试获取文件详情"""
    if not file_id:
        return
    
    response = requests.get(f"{BASE_URL}/dbc-files/{file_id}")
    print(f"文件详情: {response.status_code}")
    if response.status_code == 200:
        file_info = response.json()
        print(f"  文件名: {file_info['name']}")
        print(f"  版本数: {len(file_info['versions'])}")

def test_get_versions(file_id):
    """测试获取版本列表"""
    if not file_id:
        return
    
    response = requests.get(f"{BASE_URL}/dbc-files/{file_id}/versions")
    print(f"版本列表: {response.status_code}")
    if response.status_code == 200:
        versions = response.json()
        print(f"找到 {len(versions)} 个版本")
        for version in versions:
            print(f"  - 版本 {version['version_number']}: {version['comment'] or '无说明'}")

def test_statistics():
    """测试统计信息"""
    response = requests.get(f"{BASE_URL}/dbc-files/stats")
    print(f"统计信息: {response.status_code} - {response.json()}")

def main():
    """主测试函数"""
    print("=== DBC文件管理系统API测试 ===\n")
    
    # 测试健康检查
    test_health_check()
    print()
    
    # 测试文件上传
    file_id = test_upload_file()
    print()
    
    # 测试文件列表
    test_list_files()
    print()
    
    # 测试文件详情
    test_get_file_detail(file_id)
    print()
    
    # 测试版本列表
    test_get_versions(file_id)
    print()
    
    # 测试统计信息
    test_statistics()
    print()
    
    print("=== 测试完成 ===")
    print("请访问 http://localhost:8000/docs 查看完整的API文档")

if __name__ == "__main__":
    main()
