// DBC文件管理系统前端JavaScript

// API基础URL
const API_BASE = '/api/v1';

// 全局变量
let currentFiles = [];
let currentFileId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    showFileList();
    
    // 绑定事件
    bindEvents();
    
    // 加载文件列表
    loadFileList();
});

// 绑定事件
function bindEvents() {
    // 上传表单提交
    document.getElementById('uploadForm').addEventListener('submit', handleFileUpload);
    
    // 文件选择变化
    document.getElementById('fileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // 自动填充文件名
            const fileName = file.name.replace('.dbc', '');
            document.getElementById('fileName').value = fileName;
        }
    });
    
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        filterFileList(searchTerm);
    });
}

// 显示文件列表页面
function showFileList() {
    hideAllPages();
    document.getElementById('fileListPage').style.display = 'block';
    updateNavigation('fileList');
}

// 显示上传页面
function showUpload() {
    hideAllPages();
    document.getElementById('uploadPage').style.display = 'block';
    updateNavigation('upload');
    
    // 重置表单
    document.getElementById('uploadForm').reset();
}

// 显示统计页面
function showStats() {
    hideAllPages();
    document.getElementById('statsPage').style.display = 'block';
    updateNavigation('stats');
    
    // 加载统计数据
    loadStats();
}

// 隐藏所有页面
function hideAllPages() {
    const pages = document.querySelectorAll('.page-content');
    pages.forEach(page => page.style.display = 'none');
}

// 更新导航状态
function updateNavigation(activePage) {
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    
    // 根据页面设置活动状态
    const pageMap = {
        'fileList': 0,
        'upload': 1,
        'stats': 2
    };
    
    if (pageMap[activePage] !== undefined) {
        navLinks[pageMap[activePage]].classList.add('active');
    }
}

// 加载文件列表
async function loadFileList() {
    try {
        showLoading('fileList');
        
        const response = await fetch(`${API_BASE}/dbc-files`);
        if (!response.ok) {
            throw new Error('加载文件列表失败');
        }
        
        const files = await response.json();
        currentFiles = files;
        
        displayFileList(files);
        hideLoading('fileList');
        
    } catch (error) {
        console.error('Error loading file list:', error);
        showToast('加载文件列表失败: ' + error.message, 'error');
        hideLoading('fileList');
    }
}

// 显示文件列表
function displayFileList(files) {
    const tbody = document.getElementById('fileListBody');
    const emptyDiv = document.getElementById('fileListEmpty');
    const contentDiv = document.getElementById('fileListContent');
    
    if (files.length === 0) {
        contentDiv.style.display = 'none';
        emptyDiv.style.display = 'block';
        return;
    }
    
    emptyDiv.style.display = 'none';
    contentDiv.style.display = 'block';
    
    tbody.innerHTML = files.map(file => `
        <tr>
            <td>
                <i class="bi bi-file-earmark-code text-success me-2"></i>
                <strong>${escapeHtml(file.name)}</strong>
            </td>
            <td>${escapeHtml(file.original_filename)}</td>
            <td>${escapeHtml(file.description || '-')}</td>
            <td>
                ${file.latest_version_number ? 
                    `<span class="badge bg-primary">v${file.latest_version_number}</span>` : 
                    '<span class="text-muted">-</span>'
                }
            </td>
            <td class="file-size">${formatFileSize(file.file_size)}</td>
            <td class="time-display">${formatDateTime(file.created_at)}</td>
            <td class="action-buttons">
                <button class="btn btn-sm btn-outline-primary" onclick="showFileDetail(${file.id})" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="showVersions(${file.id})" title="版本管理">
                    <i class="bi bi-layers"></i>
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="downloadFile(${file.id})" title="下载">
                    <i class="bi bi-download"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="editFile(${file.id})" title="编辑">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteFile(${file.id})" title="删除">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 过滤文件列表
function filterFileList(searchTerm) {
    if (!searchTerm) {
        displayFileList(currentFiles);
        return;
    }
    
    const filteredFiles = currentFiles.filter(file => 
        file.name.toLowerCase().includes(searchTerm) ||
        file.original_filename.toLowerCase().includes(searchTerm) ||
        (file.description && file.description.toLowerCase().includes(searchTerm))
    );
    
    displayFileList(filteredFiles);
}

// 处理文件上传
async function handleFileUpload(e) {
    e.preventDefault();
    
    const formData = new FormData();
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showToast('请选择文件', 'error');
        return;
    }
    
    if (!file.name.toLowerCase().endsWith('.dbc')) {
        showToast('只支持.dbc格式文件', 'error');
        return;
    }
    
    // 构建表单数据
    formData.append('file', file);
    formData.append('name', document.getElementById('fileName').value);
    formData.append('description', document.getElementById('fileDescription').value);
    formData.append('comment', document.getElementById('versionComment').value);
    formData.append('created_by', document.getElementById('createdBy').value);
    
    try {
        // 显示上传进度
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';
        submitBtn.disabled = true;
        
        const response = await fetch(`${API_BASE}/dbc-files/upload`, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '上传失败');
        }
        
        const result = await response.json();
        showToast('文件上传成功!', 'success');
        
        // 重置表单并返回文件列表
        document.getElementById('uploadForm').reset();
        showFileList();
        loadFileList();
        
    } catch (error) {
        console.error('Upload error:', error);
        showToast('上传失败: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-upload"></i> 上传文件';
        submitBtn.disabled = false;
    }
}

// 显示文件详情
async function showFileDetail(fileId) {
    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}`);
        if (!response.ok) {
            throw new Error('获取文件详情失败');
        }
        
        const file = await response.json();
        
        const content = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td><strong>文件名:</strong></td><td>${escapeHtml(file.name)}</td></tr>
                        <tr><td><strong>原始文件名:</strong></td><td>${escapeHtml(file.original_filename)}</td></tr>
                        <tr><td><strong>描述:</strong></td><td>${escapeHtml(file.description || '-')}</td></tr>
                        <tr><td><strong>创建时间:</strong></td><td>${formatDateTime(file.created_at)}</td></tr>
                        <tr><td><strong>更新时间:</strong></td><td>${formatDateTime(file.updated_at)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>版本信息</h6>
                    <p>总版本数: <span class="badge bg-primary">${file.versions.length}</span></p>
                    ${file.versions.length > 0 ? `
                        <div class="version-history">
                            ${file.versions.map((version, index) => `
                                <div class="version-item ${index === 0 ? 'current' : ''}">
                                    <div class="d-flex justify-content-between">
                                        <strong>版本 ${version.version_number}</strong>
                                        <small class="text-muted">${formatDateTime(version.created_at)}</small>
                                    </div>
                                    <div class="text-muted small">
                                        大小: ${formatFileSize(version.file_size)} | 
                                        创建者: ${escapeHtml(version.created_by || '未知')}
                                    </div>
                                    ${version.comment ? `<div class="small">${escapeHtml(version.comment)}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : '<p class="text-muted">暂无版本</p>'}
                </div>
            </div>
        `;
        
        document.getElementById('fileDetailContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('fileDetailModal')).show();
        
    } catch (error) {
        console.error('Error loading file detail:', error);
        showToast('获取文件详情失败: ' + error.message, 'error');
    }
}

// 工具函数
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatFileSize(bytes) {
    if (!bytes) return '-';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showLoading(type) {
    if (type === 'fileList') {
        document.getElementById('fileListLoading').style.display = 'block';
        document.getElementById('fileListContent').style.display = 'none';
        document.getElementById('fileListEmpty').style.display = 'none';
    }
}

function hideLoading(type) {
    if (type === 'fileList') {
        document.getElementById('fileListLoading').style.display = 'none';
    }
}

function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const toastBody = document.getElementById('toastBody');

    // 设置消息内容
    toastBody.textContent = message;

    // 设置样式
    toast.className = 'toast';
    if (type === 'success') {
        toast.classList.add('bg-success', 'text-white');
    } else if (type === 'error') {
        toast.classList.add('bg-danger', 'text-white');
    } else if (type === 'warning') {
        toast.classList.add('bg-warning');
    }

    // 显示Toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 下载文件
async function downloadFile(fileId, version = null) {
    try {
        let url = `${API_BASE}/dbc-files/${fileId}/download`;
        if (version) {
            url += `?version=${version}`;
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('下载失败');
        }

        // 获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        let filename = 'download.dbc';
        if (contentDisposition) {
            const matches = contentDisposition.match(/filename="(.+)"/);
            if (matches) {
                filename = matches[1];
            }
        }

        // 下载文件
        const blob = await response.blob();
        const url2 = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url2;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url2);
        document.body.removeChild(a);

        showToast('文件下载成功', 'success');

    } catch (error) {
        console.error('Download error:', error);
        showToast('下载失败: ' + error.message, 'error');
    }
}

// 删除文件
async function deleteFile(fileId) {
    if (!confirm('确定要删除这个文件吗？删除后可以在回收站中恢复。')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '删除失败');
        }

        showToast('文件删除成功', 'success');
        loadFileList();

    } catch (error) {
        console.error('Delete error:', error);
        showToast('删除失败: ' + error.message, 'error');
    }
}

// 编辑文件
function editFile(fileId) {
    const file = currentFiles.find(f => f.id === fileId);
    if (!file) {
        showToast('文件不存在', 'error');
        return;
    }

    // 填充编辑表单
    document.getElementById('editFileId').value = fileId;
    document.getElementById('editFileName').value = file.name;
    document.getElementById('editFileDescription').value = file.description || '';

    // 显示模态框
    new bootstrap.Modal(document.getElementById('editFileModal')).show();
}

// 保存文件编辑
async function saveFileEdit() {
    const fileId = document.getElementById('editFileId').value;
    const name = document.getElementById('editFileName').value;
    const description = document.getElementById('editFileDescription').value;

    if (!name.trim()) {
        showToast('文件名不能为空', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name.trim(),
                description: description.trim() || null
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '更新失败');
        }

        showToast('文件信息更新成功', 'success');
        bootstrap.Modal.getInstance(document.getElementById('editFileModal')).hide();
        loadFileList();

    } catch (error) {
        console.error('Update error:', error);
        showToast('更新失败: ' + error.message, 'error');
    }
}

// 显示版本管理
async function showVersions(fileId) {
    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}/versions`);
        if (!response.ok) {
            throw new Error('获取版本列表失败');
        }

        const versions = await response.json();
        currentFileId = fileId;

        const content = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6>版本历史</h6>
                <span class="badge bg-primary">共 ${versions.length} 个版本</span>
            </div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>版本</th>
                            <th>大小</th>
                            <th>创建时间</th>
                            <th>创建者</th>
                            <th>说明</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${versions.map((version, index) => `
                            <tr ${index === 0 ? 'class="table-primary"' : ''}>
                                <td>
                                    <strong>v${version.version_number}</strong>
                                    ${index === 0 ? '<span class="badge bg-success ms-1">最新</span>' : ''}
                                </td>
                                <td class="file-size">${formatFileSize(version.file_size)}</td>
                                <td class="time-display">${formatDateTime(version.created_at)}</td>
                                <td>${escapeHtml(version.created_by || '-')}</td>
                                <td>${escapeHtml(version.comment || '-')}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-success" onclick="downloadFile(${fileId}, ${version.version_number})" title="下载此版本">
                                        <i class="bi bi-download"></i>
                                    </button>
                                    ${index > 0 ? `
                                        <button class="btn btn-sm btn-outline-warning" onclick="rollbackToVersion(${fileId}, ${version.version_number})" title="回滚到此版本">
                                            <i class="bi bi-arrow-counterclockwise"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteVersion(${fileId}, ${version.version_number})" title="删除此版本">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    ` : ''}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        document.getElementById('versionContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('versionModal')).show();

    } catch (error) {
        console.error('Error loading versions:', error);
        showToast('获取版本列表失败: ' + error.message, 'error');
    }
}

// 回滚到指定版本
async function rollbackToVersion(fileId, versionNumber) {
    const comment = prompt('请输入回滚说明（可选）:');
    if (comment === null) return; // 用户取消

    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}/versions/${versionNumber}/rollback`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                comment: comment || `回滚到版本 ${versionNumber}`,
                created_by: '用户'
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '回滚失败');
        }

        const result = await response.json();
        showToast(`回滚成功，新版本号: ${result.new_version_number}`, 'success');

        // 刷新版本列表
        showVersions(fileId);
        loadFileList();

    } catch (error) {
        console.error('Rollback error:', error);
        showToast('回滚失败: ' + error.message, 'error');
    }
}

// 删除版本
async function deleteVersion(fileId, versionNumber) {
    if (!confirm(`确定要删除版本 ${versionNumber} 吗？此操作不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/dbc-files/${fileId}/versions/${versionNumber}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '删除版本失败');
        }

        showToast('版本删除成功', 'success');

        // 刷新版本列表
        showVersions(fileId);
        loadFileList();

    } catch (error) {
        console.error('Delete version error:', error);
        showToast('删除版本失败: ' + error.message, 'error');
    }
}

// 加载统计信息
async function loadStats() {
    try {
        const response = await fetch(`${API_BASE}/dbc-files/stats`);
        if (!response.ok) {
            throw new Error('获取统计信息失败');
        }

        const stats = await response.json();

        document.getElementById('totalFiles').textContent = stats.total_files;
        document.getElementById('totalVersions').textContent = stats.total_versions;
        document.getElementById('totalSize').textContent = stats.total_storage_size_mb + ' MB';

    } catch (error) {
        console.error('Error loading stats:', error);
        showToast('获取统计信息失败: ' + error.message, 'error');
    }
}
