#!/usr/bin/env python3
"""
DBC文件管理系统安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装 {package} 失败")
        return False

def main():
    """主安装函数"""
    print("=== DBC文件管理系统安装程序 ===\n")
    
    # 需要安装的包列表
    packages = [
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",
        "sqlalchemy",
        "python-dotenv",
        "pydantic",
        "aiofiles",
        "cantools",
        "requests"
    ]
    
    print("开始安装依赖包...\n")
    
    failed_packages = []
    for package in packages:
        print(f"正在安装 {package}...")
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n=== 安装结果 ===")
    if failed_packages:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包或检查网络连接")
    else:
        print("✅ 所有依赖包安装成功！")
    
    # 创建环境配置文件
    if not os.path.exists(".env"):
        print("\n创建环境配置文件...")
        try:
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print("✅ 已创建 .env 配置文件")
        except FileNotFoundError:
            print("❌ .env.example 文件不存在")
    
    # 创建上传目录
    upload_dir = "./uploads"
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
        print(f"✅ 已创建上传目录: {upload_dir}")
    
    print("\n=== 安装完成 ===")
    print("现在可以运行以下命令启动服务:")
    print("python run.py")
    print("\n或者:")
    print("uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")

if __name__ == "__main__":
    main()
