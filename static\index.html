<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DBC文件管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-file-earmark-code"></i>
                DBC文件管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showFileList()">
                            <i class="bi bi-list-ul"></i> 文件列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showUpload()">
                            <i class="bi bi-cloud-upload"></i> 上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showStats()">
                            <i class="bi bi-bar-chart"></i> 统计信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/docs" target="_blank">
                            <i class="bi bi-book"></i> API文档
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container mt-4">
        <!-- 文件列表页面 -->
        <div id="fileListPage" class="page-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-folder"></i> DBC文件列表</h2>
                <div class="d-flex gap-2">
                    <input type="text" id="searchInput" class="form-control" placeholder="搜索文件..." style="width: 300px;">
                    <button class="btn btn-primary" onclick="showUpload()">
                        <i class="bi bi-plus-circle"></i> 上传文件
                    </button>
                    <button class="btn btn-outline-secondary" onclick="loadFileList()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
            
            <!-- 文件列表表格 -->
            <div class="card">
                <div class="card-body">
                    <div id="fileListLoading" class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    <div id="fileListContent" style="display: none;">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>文件名</th>
                                    <th>原始文件名</th>
                                    <th>描述</th>
                                    <th>最新版本</th>
                                    <th>文件大小</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="fileListBody">
                            </tbody>
                        </table>
                    </div>
                    <div id="fileListEmpty" class="text-center py-4" style="display: none;">
                        <i class="bi bi-folder-x display-1 text-muted"></i>
                        <p class="text-muted mt-3">暂无文件</p>
                        <button class="btn btn-primary" onclick="showUpload()">
                            <i class="bi bi-plus-circle"></i> 上传第一个文件
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 上传文件页面 -->
        <div id="uploadPage" class="page-content" style="display: none;">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="bi bi-cloud-upload"></i> 上传DBC文件</h3>
                        </div>
                        <div class="card-body">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="fileInput" class="form-label">选择DBC文件 *</label>
                                    <input type="file" class="form-control" id="fileInput" accept=".dbc" required>
                                    <div class="form-text">只支持.dbc格式文件，最大10MB</div>
                                </div>
                                <div class="mb-3">
                                    <label for="fileName" class="form-label">文件名 *</label>
                                    <input type="text" class="form-control" id="fileName" required>
                                    <div class="form-text">用于系统中显示的文件名</div>
                                </div>
                                <div class="mb-3">
                                    <label for="fileDescription" class="form-label">描述</label>
                                    <textarea class="form-control" id="fileDescription" rows="3" placeholder="文件描述信息（可选）"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="versionComment" class="form-label">版本说明</label>
                                    <input type="text" class="form-control" id="versionComment" placeholder="版本说明（可选）">
                                </div>
                                <div class="mb-3">
                                    <label for="createdBy" class="form-label">创建者</label>
                                    <input type="text" class="form-control" id="createdBy" placeholder="创建者姓名（可选）">
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-upload"></i> 上传文件
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="showFileList()">
                                        <i class="bi bi-arrow-left"></i> 返回列表
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息页面 -->
        <div id="statsPage" class="page-content" style="display: none;">
            <h2><i class="bi bi-bar-chart"></i> 统计信息</h2>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-files display-4 text-primary"></i>
                            <h4 class="mt-2" id="totalFiles">-</h4>
                            <p class="text-muted">总文件数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-layers display-4 text-success"></i>
                            <h4 class="mt-2" id="totalVersions">-</h4>
                            <p class="text-muted">总版本数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-hdd display-4 text-warning"></i>
                            <h4 class="mt-2" id="totalSize">-</h4>
                            <p class="text-muted">存储大小</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-speedometer2 display-4 text-info"></i>
                            <h4 class="mt-2">在线</h4>
                            <p class="text-muted">系统状态</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <!-- 文件详情模态框 -->
    <div class="modal fade" id="fileDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">文件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="fileDetailContent">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 版本管理模态框 -->
    <div class="modal fade" id="versionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">版本管理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="versionContent">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑文件模态框 -->
    <div class="modal fade" id="editFileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑文件信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editFileForm">
                        <input type="hidden" id="editFileId">
                        <div class="mb-3">
                            <label for="editFileName" class="form-label">文件名</label>
                            <input type="text" class="form-control" id="editFileName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editFileDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="editFileDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveFileEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
                <!-- 通知内容 -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
