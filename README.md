# DBC文件管理系统

基于FastAPI的DBC文件管理后台系统，支持DBC文件的上传、版本管理、删除、重命名等操作。

## 功能特性

- ✅ **文件上传**: 支持DBC文件上传，自动验证文件格式
- ✅ **版本管理**: 完整的版本控制系统，支持版本创建、查看、回滚
- ✅ **文件操作**: 支持文件删除、重命名、下载等基础操作
- ✅ **软删除**: 支持软删除和恢复功能
- ✅ **文件验证**: 使用cantools库验证DBC文件格式
- ✅ **统计信息**: 提供文件和存储统计信息
- ✅ **RESTful API**: 完整的REST API接口
- ✅ **自动文档**: 自动生成API文档

## 技术栈

- **后端框架**: FastAPI
- **数据库**: SQLAlchemy + SQLite (可扩展到PostgreSQL/MySQL)
- **文件处理**: cantools (DBC文件解析)
- **文档**: 自动生成OpenAPI文档

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和存储路径等参数。

### 3. 启动服务

```bash
python run.py
```

或者使用uvicorn直接启动：
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问API文档

启动后访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API接口

### 文件管理

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/dbc-files/upload` | 上传DBC文件 |
| GET | `/api/v1/dbc-files` | 获取文件列表 |
| GET | `/api/v1/dbc-files/{file_id}` | 获取文件详情 |
| PUT | `/api/v1/dbc-files/{file_id}` | 更新文件信息 |
| DELETE | `/api/v1/dbc-files/{file_id}` | 删除文件（软删除） |
| DELETE | `/api/v1/dbc-files/{file_id}/permanent` | 永久删除文件 |
| GET | `/api/v1/dbc-files/{file_id}/download` | 下载文件 |

### 版本管理

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/dbc-files/{file_id}/versions` | 获取文件版本列表 |
| GET | `/api/v1/dbc-files/{file_id}/versions/{version}` | 获取指定版本详情 |
| POST | `/api/v1/dbc-files/{file_id}/versions/{version}/rollback` | 回滚到指定版本 |
| DELETE | `/api/v1/dbc-files/{file_id}/versions/{version}` | 删除指定版本 |

### 其他功能

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/dbc-files/stats` | 获取统计信息 |
| GET | `/api/v1/dbc-files/deleted` | 获取已删除文件列表 |
| POST | `/api/v1/dbc-files/{file_id}/restore` | 恢复已删除文件 |

## 项目结构

```
.
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用主文件
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── models.py            # 数据库模型
│   ├── schemas.py           # Pydantic模型
│   ├── utils.py             # 工具函数
│   └── routers/
│       ├── __init__.py
│       └── dbc_files.py     # DBC文件相关路由
├── uploads/                 # 文件存储目录
├── requirements.txt         # 依赖包列表
├── .env.example            # 环境配置示例
├── run.py                  # 启动脚本
└── README.md               # 项目说明
```

## 配置说明

主要配置项（在 `.env` 文件中）：

- `DATABASE_URL`: 数据库连接字符串
- `UPLOAD_DIR`: 文件上传存储目录
- `MAX_FILE_SIZE`: 最大文件大小限制（字节）
- `SECRET_KEY`: 应用密钥
- `DEBUG`: 调试模式开关

## 开发说明

### 添加新功能

1. 在 `app/models.py` 中添加数据库模型
2. 在 `app/schemas.py` 中添加Pydantic模型
3. 在 `app/routers/` 中添加路由处理
4. 在 `app/main.py` 中注册路由

### 数据库迁移

如果修改了数据库模型，需要重新创建数据库：
```bash
rm dbc_manager.db  # 删除现有数据库
python -c "from app.database import engine, Base; Base.metadata.create_all(bind=engine)"
```

## 许可证

MIT License
