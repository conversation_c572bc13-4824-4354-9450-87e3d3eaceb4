# DBC文件管理系统

基于FastAPI的DBC文件管理系统，提供完整的Web UI界面和RESTful API，支持DBC文件的上传、版本管理、删除、重命名等操作。

## 🌟 功能特性

### 核心功能
- ✅ **文件上传**: 支持DBC文件上传，自动验证文件格式
- ✅ **版本管理**: 完整的版本控制系统，支持版本创建、查看、回滚
- ✅ **文件操作**: 支持文件删除、重命名、下载等基础操作
- ✅ **软删除**: 支持软删除和恢复功能
- ✅ **文件验证**: 使用cantools库验证DBC文件格式
- ✅ **统计信息**: 提供文件和存储统计信息

### 界面特性
- ✅ **现代化UI**: 基于Bootstrap 5的响应式界面
- ✅ **用户友好**: 直观的操作界面，支持拖拽上传
- ✅ **移动端适配**: 完美支持手机和平板设备
- ✅ **管理后台**: 专业的管理界面，支持高级操作
- ✅ **实时反馈**: Toast通知和加载状态提示

### 技术特性
- ✅ **RESTful API**: 完整的REST API接口
- ✅ **自动文档**: 自动生成API文档
- ✅ **类型安全**: 使用Pydantic进行数据验证
- ✅ **异步处理**: 基于FastAPI的高性能异步框架

## 🛠️ 技术栈

### 后端
- **框架**: FastAPI
- **数据库**: SQLAlchemy + SQLite (可扩展到PostgreSQL/MySQL)
- **文件处理**: cantools (DBC文件解析)
- **文档**: 自动生成OpenAPI文档

### 前端
- **UI框架**: Bootstrap 5
- **图标**: Bootstrap Icons
- **JavaScript**: 原生ES6+
- **样式**: 响应式CSS3

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
# 1. 安装依赖
python install.py

# 2. 启动服务（自动打开浏览器）
python start.py
```

### 方法二：手动启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境
cp .env.example .env

# 3. 启动服务
python run.py
```

### 访问地址

启动后可以访问以下地址：

| 功能 | 地址 | 描述 |
|------|------|------|
| 🏠 **主页** | http://localhost:8000 | 用户界面，文件管理 |
| 🔧 **管理后台** | http://localhost:8000/admin | 管理员界面，高级功能 |
| 📚 **API文档** | http://localhost:8000/docs | Swagger UI文档 |
| 📖 **API文档** | http://localhost:8000/redoc | ReDoc文档 |

## 📱 界面预览

### 主界面功能
- **文件列表**: 查看所有DBC文件，支持搜索和筛选
- **文件上传**: 拖拽或点击上传DBC文件
- **版本管理**: 查看文件版本历史，支持回滚
- **文件操作**: 下载、重命名、删除文件
- **统计信息**: 查看系统使用统计

### 管理后台功能
- **文件管理**: 高级文件管理功能
- **回收站**: 管理已删除的文件
- **系统统计**: 详细的系统使用统计
- **系统信息**: 查看系统配置信息

## API接口

### 文件管理

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/dbc-files/upload` | 上传DBC文件 |
| GET | `/api/v1/dbc-files` | 获取文件列表 |
| GET | `/api/v1/dbc-files/{file_id}` | 获取文件详情 |
| PUT | `/api/v1/dbc-files/{file_id}` | 更新文件信息 |
| DELETE | `/api/v1/dbc-files/{file_id}` | 删除文件（软删除） |
| DELETE | `/api/v1/dbc-files/{file_id}/permanent` | 永久删除文件 |
| GET | `/api/v1/dbc-files/{file_id}/download` | 下载文件 |

### 版本管理

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/dbc-files/{file_id}/versions` | 获取文件版本列表 |
| GET | `/api/v1/dbc-files/{file_id}/versions/{version}` | 获取指定版本详情 |
| POST | `/api/v1/dbc-files/{file_id}/versions/{version}/rollback` | 回滚到指定版本 |
| DELETE | `/api/v1/dbc-files/{file_id}/versions/{version}` | 删除指定版本 |

### 其他功能

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/dbc-files/stats` | 获取统计信息 |
| GET | `/api/v1/dbc-files/deleted` | 获取已删除文件列表 |
| POST | `/api/v1/dbc-files/{file_id}/restore` | 恢复已删除文件 |

## 📁 项目结构

```
.
├── app/                     # 后端应用
│   ├── __init__.py
│   ├── main.py              # FastAPI应用主文件
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── models.py            # 数据库模型
│   ├── schemas.py           # Pydantic模型
│   ├── utils.py             # 工具函数
│   └── routers/
│       ├── __init__.py
│       └── dbc_files.py     # DBC文件相关路由
├── static/                  # 前端静态资源
│   ├── css/
│   │   └── style.css        # 样式文件
│   ├── js/
│   │   ├── app.js           # 主应用JavaScript
│   │   └── admin.js         # 管理后台JavaScript
│   ├── index.html           # 主页面
│   └── admin.html           # 管理后台页面
├── uploads/                 # 文件存储目录
├── requirements.txt         # 依赖包列表
├── .env.example            # 环境配置示例
├── install.py              # 依赖安装脚本
├── start.py                # 一键启动脚本
├── run.py                  # 服务启动脚本
├── test_api.py             # API测试脚本
└── README.md               # 项目说明
```

## 配置说明

主要配置项（在 `.env` 文件中）：

- `DATABASE_URL`: 数据库连接字符串
- `UPLOAD_DIR`: 文件上传存储目录
- `MAX_FILE_SIZE`: 最大文件大小限制（字节）
- `SECRET_KEY`: 应用密钥
- `DEBUG`: 调试模式开关

## 开发说明

### 添加新功能

1. 在 `app/models.py` 中添加数据库模型
2. 在 `app/schemas.py` 中添加Pydantic模型
3. 在 `app/routers/` 中添加路由处理
4. 在 `app/main.py` 中注册路由

### 数据库迁移

如果修改了数据库模型，需要重新创建数据库：
```bash
rm dbc_manager.db  # 删除现有数据库
python -c "from app.database import engine, Base; Base.metadata.create_all(bind=engine)"
```

## 许可证

MIT License
