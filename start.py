#!/usr/bin/env python3
"""
DBC文件管理系统启动脚本
包含前后端一体化启动
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import cantools
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行 'python install.py' 安装依赖")
        return False

def create_database():
    """创建数据库表"""
    try:
        from app.database import engine, Base
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:8000')
        print("🌐 已打开浏览器")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:8000")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 DBC文件管理系统启动程序")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建必要目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    print("✅ 目录检查完成")
    
    # 初始化数据库
    if not create_database():
        return
    
    # 检查环境配置
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ 已创建环境配置文件")
        else:
            print("⚠️  未找到环境配置文件")
    
    print("\n🎯 启动服务...")
    print("📍 前台页面: http://localhost:8000")
    print("🔧 管理后台: http://localhost:8000/admin")
    print("📚 API文档: http://localhost:8000/docs")
    print("\n按 Ctrl+C 停止服务\n")
    
    # 延迟打开浏览器
    Timer(2.0, open_browser).start()
    
    try:
        # 启动FastAPI服务
        from app.main import app
        import uvicorn
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请检查端口8000是否被占用")

if __name__ == "__main__":
    main()
