import os
from typing import Optional


class Settings:
    """应用配置类"""
    def __init__(self):
        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./dbc_manager.db")

        # File Storage Configuration
        self.upload_dir = os.getenv("UPLOAD_DIR", "./uploads")
        self.max_file_size = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB

        # Security Configuration
        self.secret_key = os.getenv("SECRET_KEY", "your-secret-key-here")
        self.algorithm = os.getenv("ALGORITHM", "HS256")
        self.access_token_expire_minutes = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

        # Application Configuration
        self.app_name = os.getenv("APP_NAME", "DBC File Manager")
        self.app_version = os.getenv("APP_VERSION", "1.0.0")
        self.debug = os.getenv("DEBUG", "True").lower() == "true"


# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

settings = Settings()

# Ensure upload directory exists
os.makedirs(settings.upload_dir, exist_ok=True)
