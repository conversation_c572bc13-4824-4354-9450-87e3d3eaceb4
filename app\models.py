from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class DBCFile(Base):
    """DBC文件主表"""
    __tablename__ = "dbc_files"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    original_filename = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    is_deleted = Column(Boolean, default=False)
    
    # 关联版本
    versions = relationship("DBCFileVersion", back_populates="dbc_file", cascade="all, delete-orphan")
    
    @property
    def latest_version(self):
        """获取最新版本"""
        return max(self.versions, key=lambda v: v.version_number) if self.versions else None


class DBCFileVersion(Base):
    """DBC文件版本表"""
    __tablename__ = "dbc_file_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    dbc_file_id = Column(Integer, ForeignKey("dbc_files.id"), nullable=False)
    version_number = Column(Integer, nullable=False)
    file_path = Column(String(500), nullable=False)  # 文件在服务器上的存储路径
    file_size = Column(Integer, nullable=False)  # 文件大小（字节）
    file_hash = Column(String(64), nullable=False)  # 文件MD5哈希值
    comment = Column(Text, nullable=True)  # 版本说明
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(String(100), nullable=True)  # 创建者
    
    # 关联主文件
    dbc_file = relationship("DBCFile", back_populates="versions")
    
    def __repr__(self):
        return f"<DBCFileVersion(id={self.id}, version={self.version_number})>"
